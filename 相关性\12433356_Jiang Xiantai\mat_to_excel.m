function mat_to_excel()
    % 将.mat文件转换为Excel文件的MATLAB脚本
    
    % 获取当前目录
    current_dir = pwd;
    
    % 要转换的.mat文件列表
    mat_files = {'HR_pr.mat', 'HR_rel.mat'};
    
    for i = 1:length(mat_files)
        mat_file = mat_files{i};
        mat_path = fullfile(current_dir, mat_file);
        
        if exist(mat_path, 'file')
            % 生成Excel文件名
            [~, name, ~] = fileparts(mat_file);
            excel_file = [name, '.xlsx'];
            excel_path = fullfile(current_dir, excel_file);
            
            fprintf('\n开始转换: %s\n', mat_file);
            
            try
                % 加载.mat文件
                data = load(mat_path);
                
                % 获取变量名
                var_names = fieldnames(data);
                
                % 删除已存在的Excel文件（如果有）
                if exist(excel_path, 'file')
                    delete(excel_path);
                end
                
                % 遍历所有变量
                for j = 1:length(var_names)
                    var_name = var_names{j};
                    var_data = data.(var_name);
                    
                    fprintf('处理变量: %s\n', var_name);
                    
                    % 处理不同类型的数据
                    if isnumeric(var_data)
                        % 数值数据
                        if ismatrix(var_data) && size(var_data, 1) > 1 && size(var_data, 2) > 1
                            % 二维矩阵
                            writematrix(var_data, excel_path, 'Sheet', var_name);
                        elseif isvector(var_data)
                            % 向量数据
                            if isrow(var_data)
                                var_data = var_data'; % 转为列向量
                            end
                            writematrix(var_data, excel_path, 'Sheet', var_name);
                        else
                            % 其他数值数据
                            writematrix(var_data, excel_path, 'Sheet', var_name);
                        end
                    elseif iscell(var_data)
                        % 单元格数组
                        try
                            writecell(var_data, excel_path, 'Sheet', var_name);
                        catch
                            % 如果写入失败，尝试转换为字符串
                            str_data = cellfun(@(x) string(x), var_data, 'UniformOutput', false);
                            writecell(str_data, excel_path, 'Sheet', var_name);
                        end
                    elseif ischar(var_data) || isstring(var_data)
                        % 字符或字符串数据
                        if ischar(var_data)
                            var_data = string(var_data);
                        end
                        writematrix(var_data, excel_path, 'Sheet', var_name);
                    else
                        % 其他类型数据，尝试转换为字符串
                        try
                            str_data = string(var_data);
                            writematrix(str_data, excel_path, 'Sheet', var_name);
                        catch
                            fprintf('警告: 无法处理变量 %s 的数据类型\n', var_name);
                        end
                    end
                    
                    fprintf('变量 %s 已写入工作表\n', var_name);
                end
                
                fprintf('转换完成！Excel文件已保存为: %s\n', excel_path);
                
            catch ME
                fprintf('转换过程中出现错误: %s\n', ME.message);
            end
            
        else
            fprintf('文件不存在: %s\n', mat_path);
        end
    end
    
    fprintf('\n所有转换任务完成！\n');
end
