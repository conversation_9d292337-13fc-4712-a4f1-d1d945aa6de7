# 这是一份放在 D:\Test 文件夹中的 run.py 脚本

import scipy.io
import matplotlib.pyplot as plt
import numpy as np

# --- 1. 参数设置 ---

# 您想查看的X轴范围
x_start = 0
x_end = 200

# 学术图表风格参数
FONT_SIZE = 14  # 坐标轴标签和图例的字体大小
TITLE_SIZE = 16  # 子图标题的字体大小
SUPTITLE_SIZE = 18  # 总标题的字体大小
LINE_WIDTH = 1.0  # 波形线条的宽度
FIGURE_DPI = 300  # 输出图片的分辨率 (300 DPI为出版级标准)

# --- 2. 数据加载 ---

file_pr = 'HR_pr.mat'
file_rel = 'HR_rel.mat'

print("--- 开始运行脚本 ---")

try:
    print(f"加载: {file_pr}")
    mat_pr_data = scipy.io.loadmat(file_pr)
    key_pr = [key for key in mat_pr_data.keys() if not key.startswith('__')][0]
    hr_pr = np.ravel(mat_pr_data[key_pr])
    print("加载成功！")

    print(f"加载: {file_rel}")
    mat_rel_data = scipy.io.loadmat(file_rel)
    key_rel = [key for key in mat_rel_data.keys() if not key.startswith('__')][0]
    hr_rel = np.ravel(mat_rel_data[key_rel])
    print("加载成功！")

    # --- 3. 绘图 ---

    print("正在生成学术风格图表...")

    # 创建一个包含2行1列的子图布局，共享X轴
    fig, axes = plt.subplots(2, 1, figsize=(12, 8), sharex=True)

    # 为整个图添加一个总标题，并增加与子图的间距
    fig.suptitle('Comparative Analysis of Waveforms', fontsize=SUPTITLE_SIZE, fontweight='bold')

    # --- 绘制第一个子图 (顶部) ---
    axes[0].plot(hr_pr, color='blue', linewidth=LINE_WIDTH, label=key_pr)  # 使用变量名作为图例
    axes[0].set_title(f'Waveform: {key_pr}', fontsize=TITLE_SIZE)
    axes[0].set_ylabel('Value', fontsize=FONT_SIZE)
    axes[0].legend(fontsize=FONT_SIZE)
    axes[0].grid(True, linestyle='--', alpha=0.6)  # 网格线变细、变淡

    # 设置刻度线样式：向内、加宽
    axes[0].tick_params(axis='both', which='major', direction='in', width=1.2, labelsize=FONT_SIZE)

    # --- 绘制第二个子图 (底部) ---
    axes[1].plot(hr_rel, color='red', linewidth=LINE_WIDTH, label=key_rel)
    axes[1].set_title(f'Waveform: {key_rel}', fontsize=TITLE_SIZE)
    axes[1].set_xlabel('Sample Index', fontsize=FONT_SIZE)
    axes[1].set_ylabel('Value', fontsize=FONT_SIZE)
    axes[1].legend(fontsize=FONT_SIZE)
    axes[1].grid(True, linestyle='--', alpha=0.6)

    axes[1].tick_params(axis='both', which='major', direction='in', width=1.2, labelsize=FONT_SIZE)

    # --- 统一设置和保存 ---

    # 设置X轴的显示范围
    axes[1].set_xlim(x_start, x_end)

    # 自动调整布局，防止重叠
    plt.tight_layout(rect=[0, 0, 1, 0.94])  # 调整布局为总标题留出更多空间

    # 以高分辨率保存图表
    output_filename = 'scientific_plot.png'
    plt.savefig(output_filename, dpi=FIGURE_DPI, bbox_inches='tight')

    print("\n--- 任务完成 ---")
    print(f"成功！学术风格图表已保存为 {output_filename}")

except FileNotFoundError as e:
    print(f"\n--- 发生错误 ---: 找不到文件 '{e.filename}'。")
except Exception as e:
    print(f"\n--- 发生错误 ---: {e}")