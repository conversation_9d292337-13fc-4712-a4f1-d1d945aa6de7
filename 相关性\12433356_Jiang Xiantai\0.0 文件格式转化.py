import scipy.io
import pandas as pd
import numpy as np
import os

def mat_to_excel(mat_file_path, excel_file_path):
    """
    将.mat文件转换为Excel文件

    参数:
    mat_file_path: .mat文件的路径
    excel_file_path: 输出Excel文件的路径
    """
    try:
        # 读取.mat文件
        mat_data = scipy.io.loadmat(mat_file_path)

        # 创建Excel写入器
        with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
            # 遍历.mat文件中的所有变量
            for key, value in mat_data.items():
                # 跳过以'__'开头的元数据
                if not key.startswith('__'):
                    print(f"处理变量: {key}")

                    # 将数据转换为DataFrame
                    if isinstance(value, np.ndarray):
                        if value.ndim == 1:
                            # 一维数组
                            df = pd.DataFrame({key: value})
                        elif value.ndim == 2:
                            # 二维数组
                            df = pd.DataFrame(value)
                        else:
                            # 多维数组，展平处理
                            df = pd.DataFrame(value.flatten(), columns=[f'{key}_flattened'])
                    else:
                        # 其他类型数据
                        df = pd.DataFrame([value], columns=[key])

                    # 将DataFrame写入Excel的不同工作表
                    sheet_name = key[:31]  # Excel工作表名称限制为31个字符
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"变量 {key} 已写入工作表 {sheet_name}")

        print(f"转换完成！Excel文件已保存为: {excel_file_path}")

    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")

def main():
    # 当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 要转换的.mat文件列表
    mat_files = ['HR_pr.mat', 'HR_rel.mat']

    for mat_file in mat_files:
        mat_path = os.path.join(current_dir, mat_file)

        if os.path.exists(mat_path):
            # 生成Excel文件名
            excel_file = mat_file.replace('.mat', '.xlsx')
            excel_path = os.path.join(current_dir, excel_file)

            print(f"\n开始转换: {mat_file}")
            mat_to_excel(mat_path, excel_path)
        else:
            print(f"文件不存在: {mat_path}")

if __name__ == "__main__":
    main()